"""
Intelligent Multi-Currency Balance-Aware Order Management

REMOVED: All fallback trading strategies and emergency modes
System must maintain full sophistication or fail gracefully
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Union
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class BalanceStatus(Enum):
    SUFFICIENT = "sufficient"
    INSUFFICIENT = "insufficient"
    UNKNOWN = "unknown"

class OrderSizingStrategy(Enum):
    FULL_AMOUNT = "full_amount"
    AVAILABLE_BALANCE = "available_balance"
    PERCENTAGE = "percentage"
    DYNAMIC = "dynamic"
    CONSERVATIVE = "conservative"

@dataclass
class BalanceCheck:
    status: BalanceStatus
    available_balance: Decimal
    required_balance: Decimal
    usable_amount: Decimal
    currency: str
    exchange: str
    confidence: float = 0.0
    last_updated: float = 0.0

@dataclass
class OrderSizing:
    recommended_amount: Decimal
    confidence: float
    risk_score: float
    strategy_used: OrderSizingStrategy
    reasoning: str
    balance_check: BalanceCheck

class BalanceAwareOrderManager:
    """CLEANED: Balance-aware order manager without fallback strategies"""
    
    def __init__(self, exchange_clients: Dict[str, Any], config: Dict[str, Any] = None):
        self.exchange_clients = exchange_clients
        self.config = config or {}
        self.balance_cache = {}
        self.cache_ttl = 30  # 30 seconds cache
        self.min_order_value = self.config.get('min_order_value', 10.0)
        self.aggressive_trading = self.config.get('aggressive_trading', True)
        
        # Order statistics
        self.order_stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'balance_failures': 0,
            'last_reset': time.time()
        }
        
        logger.info("⚖️ [BALANCE-MANAGER] Initialized balance-aware order manager")
    
    async def initialize(self):
        """Initialize the balance-aware order manager"""
        try:
            logger.info("🔧 [BALANCE-MANAGER] Initializing order manager...")
            
            # Initialize balance cache
            await self.refresh_all_balances()
            
            logger.info("✅ [BALANCE-MANAGER] Order manager initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ [BALANCE-MANAGER] Error initializing order manager: {e}")
            raise

    async def validate_order_balance(self, symbol: str, side: str, amount: Decimal,
                                   exchange: str, price: Optional[Decimal] = None) -> BalanceCheck:
        """Validate balance for a specific order"""
        try:
            logger.info(f"✅ [BALANCE-CHECK] Validating {side} {amount} {symbol} on {exchange}")

            # CRITICAL FIX: Validate amount to prevent massive orders
            if amount > 1000:  # Safety check for amounts over $1000
                logger.error(f"❌ [BALANCE-ORDER] Suspicious order amount detected: {amount}")
                logger.error(f"❌ [BALANCE-ORDER] Order details: {side} {symbol} on {exchange}")
                return BalanceCheck(
                    status=BalanceStatus.INSUFFICIENT,
                    available_balance=Decimal('0'),
                    required_balance=amount,
                    usable_amount=Decimal('0'),
                    currency='UNKNOWN',
                    exchange=exchange,
                    confidence=0.0,
                    last_updated=time.time()
                )

            # Determine required currency
            if side.lower() == 'buy':
                # For BUY orders, determine quote currency from symbol
                required_currency = self._extract_quote_currency(symbol)
            else:
                # For SELL orders, determine base currency from symbol
                required_currency = self._extract_base_currency(symbol)
            
            # Get current balance with enhanced validation
            current_balance = await self._get_current_balance(required_currency, exchange)

            # CRITICAL FIX: Validate balance data is reasonable
            max_reasonable_balance = Decimal('1000000')  # 1M units max reasonable balance
            if current_balance > max_reasonable_balance:
                logger.error(f"❌ [BALANCE-ERROR] Suspicious {required_currency} balance: {current_balance}")
                logger.error(f"❌ [BALANCE-ERROR] Balance exceeds reasonable limit, treating as 0")
                current_balance = Decimal('0')

            # Calculate required balance
            if side.lower() == 'buy':
                # CRITICAL FIX: For BUY orders, amount is already in USDT (quote currency)
                # Don't multiply by price again - that would double-convert
                required_balance = amount
                logger.debug(f"🔍 [BUY-BALANCE] Required {required_currency} for BUY order: {required_balance}")
            else:
                # For SELL orders, amount is in base currency (crypto)
                required_balance = amount
                logger.debug(f"🔍 [SELL-BALANCE] Required {required_currency} for SELL order: {required_balance}")

            # Enhanced balance validation logging
            logger.info(f"💰 [BALANCE-CHECK] {required_currency} on {exchange}")
            logger.info(f"💰 [BALANCE-CHECK] Available: {current_balance}, Required: {required_balance}")
            logger.info(f"💰 [BALANCE-CHECK] Order: {side} {amount} {symbol}")

            # Determine status
            if current_balance >= required_balance:
                status = BalanceStatus.SUFFICIENT
                usable_amount = min(amount, current_balance * Decimal('0.9'))  # Use 90% max
                logger.info(f"✅ [BALANCE-OK] Sufficient {required_currency}: {current_balance} >= {required_balance}")
            else:
                status = BalanceStatus.INSUFFICIENT
                usable_amount = current_balance * Decimal('0.9')
                # FIXED: Log as info instead of warning - this is normal for adaptive sizing
                logger.info(f"🎯 [BALANCE-ADAPTIVE] {required_currency}: {current_balance} < {required_balance} - will use adaptive sizing")
                logger.info(f"🎯 [BALANCE-ADAPTIVE] Shortfall: {required_balance - current_balance} - adaptive sizing will optimize")

                # CRITICAL FIX: For SELL orders with 0 balance, fail immediately
                if side.lower() == 'sell' and current_balance == 0:
                    logger.error(f"❌ [ZERO-BALANCE] Cannot SELL {required_currency} - balance is 0")
                    usable_amount = Decimal('0')
            
            # Calculate confidence
            confidence = self._calculate_balance_confidence(exchange)
            
            return BalanceCheck(
                status=status,
                available_balance=current_balance,
                required_balance=required_balance,
                usable_amount=usable_amount,
                currency=required_currency,
                exchange=exchange,
                confidence=confidence,
                last_updated=time.time()
            )
            
        except Exception as e:
            logger.error(f"❌ [BALANCE-CHECK] Error validating balance: {e}")
            return BalanceCheck(
                status=BalanceStatus.UNKNOWN,
                available_balance=Decimal('0'),
                required_balance=amount,
                usable_amount=Decimal('0'),
                currency='UNKNOWN',
                exchange=exchange,
                confidence=0.0,
                last_updated=time.time()
            )

    def _extract_base_currency(self, symbol: str) -> str:
        """Extract base currency from trading pair symbol"""
        try:
            # Handle different formats: BTC-USD, BTCUSDT, BTC/USD, SOLBTC
            if '-' in symbol:
                return symbol.split('-')[0]
            elif '/' in symbol:
                return symbol.split('/')[0]
            else:
                # For formats like BTCUSDT, SOLBTC, try to parse
                # Common quote currencies in order of priority
                quote_currencies = ['USDT', 'USDC', 'BTC', 'ETH', 'USD', 'EUR', 'GBP', 'JPY']

                for quote in quote_currencies:
                    if symbol.endswith(quote):
                        base = symbol[:-len(quote)]
                        if base:  # Ensure base currency is not empty
                            return base

                # Fallback: assume last 3-4 characters are quote currency
                if len(symbol) > 4:
                    return symbol[:-4]
                elif len(symbol) > 3:
                    return symbol[:-3]
                else:
                    return symbol

        except Exception as e:
            logger.debug(f"❌ [PARSE] Error extracting base currency from {symbol}: {e}")
            return symbol

    def _extract_quote_currency(self, symbol: str) -> str:
        """Extract quote currency from trading pair symbol"""
        try:
            # Handle different formats: BTC-USD, BTCUSDT, BTC/USD, SOLBTC
            if '-' in symbol:
                return symbol.split('-')[1]
            elif '/' in symbol:
                return symbol.split('/')[1]
            else:
                # For formats like BTCUSDT, SOLBTC, try to parse
                # Common quote currencies in order of priority
                quote_currencies = ['USDT', 'USDC', 'BTC', 'ETH', 'USD', 'EUR', 'GBP', 'JPY']

                for quote in quote_currencies:
                    if symbol.endswith(quote):
                        return quote

                # Fallback: assume last 3-4 characters are quote currency
                if len(symbol) > 4:
                    return symbol[-4:]
                elif len(symbol) > 3:
                    return symbol[-3:]
                else:
                    return 'UNKNOWN'

        except Exception as e:
            logger.debug(f"❌ [PARSE] Error extracting quote currency from {symbol}: {e}")
            return 'UNKNOWN'

    def _apply_decimal_precision(self, amount: Decimal, symbol: str) -> Decimal:
        """Apply appropriate decimal precision for the trading pair"""
        try:
            # Define precision rules for different types of assets
            precision_rules = {
                # Major cryptocurrencies - higher precision
                'BTC': 8,
                'ETH': 6,
                'SOL': 4,
                'ADA': 2,
                'DOT': 2,
                'LINK': 2,
                'UNI': 2,
                'AVAX': 2,
                'MATIC': 2,
                # Stablecoins - lower precision
                'USDT': 2,
                'USDC': 2,
                'USD': 2,
                # Default precision
                'DEFAULT': 6
            }

            # Extract base currency from symbol
            base_currency = self._extract_base_currency(symbol)

            # Get precision for this currency
            precision = precision_rules.get(base_currency, precision_rules['DEFAULT'])

            # Round to appropriate decimal places
            rounded_amount = amount.quantize(Decimal('0.1') ** precision)

            logger.debug(f"🔢 [PRECISION] {symbol} ({base_currency}): {amount} -> {rounded_amount} ({precision} decimals)")

            return rounded_amount

        except Exception as e:
            logger.error(f"❌ [PRECISION] Error applying decimal precision: {e}")
            # Fallback to 6 decimal places
            return amount.quantize(Decimal('0.000001'))

    async def execute_balance_aware_order(self, symbol: str, side: str, amount: Decimal,
                                        exchange: str, order_type: str = "market") -> Dict[str, Any]:
        """Execute an order with intelligent balance adaptation - CONTINUOUS TRADING"""
        try:
            # CRITICAL FIX: Validate and normalize exchange name
            exchange = self._validate_and_normalize_exchange(exchange)

            logger.info(f"⚖️ [BALANCE-ORDER] Executing balance-aware order")
            logger.info(f"⚖️ [BALANCE-ORDER] {side} {amount} {symbol} on {exchange}")

            # CRITICAL FIX: Refresh balance immediately before order calculation
            await self._refresh_balance_cache(exchange)

            # Validate balance first
            balance_check = await self.validate_order_balance(symbol, side, amount, exchange)

            if balance_check.status != BalanceStatus.SUFFICIENT:
                # INTELLIGENT ADAPTIVE SIZING: Normal operation for aggressive micro-trading
                logger.info(f"🎯 [ADAPTIVE-SIZING] Optimizing order size for available balance")
                logger.info(f"🎯 [ADAPTIVE-SIZING] Requested: {balance_check.required_balance}, Available: {balance_check.available_balance}")

                # Calculate adaptive order size (80-90% of available balance)
                adaptive_amount = await self._calculate_adaptive_order_size(
                    symbol, side, balance_check.available_balance, exchange
                )

                if adaptive_amount > Decimal('0'):
                    logger.info(f"✅ [ADAPTIVE-SIZING] Optimized order size: {amount} → {adaptive_amount}")
                    logger.info(f"✅ [ADAPTIVE-SIZING] Using {(adaptive_amount / balance_check.available_balance * 100):.1f}% of available balance")

                    # Re-validate with adaptive amount
                    balance_check = await self.validate_order_balance(symbol, side, adaptive_amount, exchange)
                    amount = adaptive_amount  # Use adaptive amount for execution
                else:
                    # Only fail if adaptive sizing also fails
                    error_msg = f"Cannot adapt order size: available balance too low ({balance_check.available_balance})"
                    logger.error(f"❌ [BALANCE-ORDER] {error_msg}")

                    self.order_stats['balance_failures'] += 1

                    return {
                        "success": False,
                        "error": error_msg,
                        "balance_check": balance_check,
                        "adaptive_sizing_attempted": True
                    }

            # Execute order with validated amount
            client = self.exchange_clients.get(exchange)
            if not client:
                return {"success": False, "error": f"Exchange {exchange} not available"}

            # Use the validated usable amount with proper decimal precision
            final_amount = self._apply_decimal_precision(balance_check.usable_amount, symbol)

            logger.info(f"💰 [BALANCE-ORDER] Using validated amount: {final_amount}")
            logger.info(f"💰 [BALANCE-ORDER] Original: {balance_check.usable_amount}, Rounded: {final_amount}")
            
            # Execute the order
            if hasattr(client, 'place_order'):
                result = await client.place_order(
                    symbol=symbol,
                    side=side,
                    amount=float(final_amount),
                    order_type=order_type,
                    is_quote_amount=(side.lower() == 'buy')
                )
                
                # Update statistics
                self.order_stats['total_orders'] += 1
                if result.get('success', False) or 'orderId' in result.get('result', {}):
                    self.order_stats['successful_orders'] += 1
                
                return {
                    "success": True,
                    "result": result,
                    "balance_check": balance_check,
                    "final_amount": float(final_amount)
                }
            else:
                return {"success": False, "error": "Exchange client does not support place_order"}

        except Exception as e:
            logger.error(f"❌ [BALANCE-ORDER] Error executing order: {e}")
            return {"success": False, "error": str(e)}

    async def _calculate_adaptive_order_size(self, symbol: str, side: str, available_balance: Decimal, exchange: str) -> Decimal:
        """Calculate adaptive order size based on available balance - AGGRESSIVE MICRO-TRADING"""
        try:
            logger.info(f"🧠 [ADAPTIVE-SIZING] Calculating adaptive order size")
            logger.info(f"🧠 [ADAPTIVE-SIZING] Available balance: {available_balance}")

            # Minimum order values by exchange
            min_order_values = {
                'bybit': Decimal('5.0'),    # Bybit minimum
                'coinbase': Decimal('1.0'), # Coinbase minimum
                'binance': Decimal('10.0')  # Binance minimum
            }

            min_order_value = min_order_values.get(exchange.lower(), Decimal('5.0'))

            # Check if balance meets minimum requirements
            if available_balance < min_order_value:
                logger.info(f"💰 [ADAPTIVE-SIZING] Balance {available_balance} below minimum {min_order_value} - skipping order")
                return Decimal('0')

            # AGGRESSIVE MICRO-TRADING: Use 80-90% of available balance
            if side.lower() == 'buy':
                # For BUY orders, use 85% of available USDT/USD balance
                adaptive_percentage = Decimal('0.85')  # 85% aggressive micro-trading
                adaptive_amount = available_balance * adaptive_percentage

                # Ensure we don't go below minimum or above 90% maximum
                max_amount = available_balance * Decimal('0.90')
                adaptive_amount = max(min_order_value, min(adaptive_amount, max_amount))

                logger.info(f"🎯 [ADAPTIVE-BUY] Using {adaptive_percentage * 100}% of balance: {adaptive_amount}")

            else:  # SELL orders
                # For SELL orders, use 85% of available crypto balance
                adaptive_percentage = Decimal('0.85')  # 85% aggressive micro-trading
                adaptive_amount = available_balance * adaptive_percentage

                # For SELL orders, ensure we have enough crypto to sell
                max_amount = available_balance * Decimal('0.90')
                adaptive_amount = min(adaptive_amount, max_amount)

                # Apply minimum position size (0.001 for most cryptos)
                min_position = Decimal('0.001')
                if adaptive_amount < min_position:
                    logger.info(f"💰 [ADAPTIVE-SELL] Position {adaptive_amount} below minimum {min_position} - skipping order")
                    return Decimal('0')

                logger.info(f"🎯 [ADAPTIVE-SELL] Using {adaptive_percentage * 100}% of balance: {adaptive_amount}")

            # Final validation - ensure adaptive amount is reasonable
            if adaptive_amount <= Decimal('0'):
                logger.info(f"💰 [ADAPTIVE-SIZING] Calculated amount is zero or negative - no suitable order size")
                return Decimal('0')

            # Apply decimal precision for the symbol
            final_amount = self._apply_decimal_precision(adaptive_amount, symbol)

            logger.info(f"✅ [ADAPTIVE-SIZING] Final adaptive amount: {final_amount}")
            return final_amount

        except Exception as e:
            logger.error(f"❌ [ADAPTIVE-SIZING] Error calculating adaptive order size: {e}")
            return Decimal('0')

    async def _refresh_balance_cache(self, exchange: str):
        """Refresh balance cache for real-time validation"""
        try:
            logger.debug(f"🔄 [BALANCE-REFRESH] Refreshing balance cache for {exchange}")

            client = self.exchange_clients.get(exchange)
            if not client:
                logger.warning(f"⚠️ [BALANCE-REFRESH] No client available for {exchange}")
                return

            # Force refresh balance data
            if hasattr(client, 'get_balance'):
                fresh_balance = await client.get_balance()
                logger.debug(f"🔄 [BALANCE-REFRESH] Fresh balance data retrieved: {fresh_balance}")
            elif hasattr(client, 'fetch_balance'):
                fresh_balance = await client.fetch_balance()
                logger.debug(f"🔄 [BALANCE-REFRESH] Fresh balance data retrieved: {fresh_balance}")
            else:
                logger.warning(f"⚠️ [BALANCE-REFRESH] No balance method available for {exchange}")

        except Exception as e:
            logger.warning(f"⚠️ [BALANCE-REFRESH] Error refreshing balance cache: {e}")
            # Don't fail the order if balance refresh fails

    def _validate_and_normalize_exchange(self, exchange: str) -> str:
        """Validate and normalize exchange name to prevent 'unknown' exchange errors"""
        try:
            if not exchange or exchange.lower() in ['unknown', 'none', 'null', '']:
                # Default to first available exchange
                if self.exchange_clients:
                    default_exchange = list(self.exchange_clients.keys())[0]
                    logger.warning(f"⚠️ [EXCHANGE-VALIDATE] Invalid exchange '{exchange}', using default: {default_exchange}")
                    return default_exchange
                else:
                    logger.error(f"❌ [EXCHANGE-VALIDATE] No exchange clients available")
                    return 'bybit'  # Fallback to bybit

            # Normalize exchange name
            exchange_normalized = exchange.lower().strip()

            # Map common variations
            exchange_mapping = {
                'bybit': 'bybit',
                'bybit_client': 'bybit',
                'bybit_fixed': 'bybit',
                'coinbase': 'coinbase',
                'coinbase_pro': 'coinbase',
                'coinbase_advanced': 'coinbase',
                'binance': 'binance',
                'kraken': 'kraken'
            }

            normalized = exchange_mapping.get(exchange_normalized, exchange_normalized)

            # Verify exchange client exists
            if normalized in self.exchange_clients:
                logger.debug(f"✅ [EXCHANGE-VALIDATE] Exchange '{exchange}' normalized to '{normalized}'")
                return normalized
            else:
                # Find closest match
                available_exchanges = list(self.exchange_clients.keys())
                for available in available_exchanges:
                    if normalized in available.lower() or available.lower() in normalized:
                        logger.warning(f"⚠️ [EXCHANGE-VALIDATE] Exchange '{exchange}' mapped to '{available}'")
                        return available

                # Use first available exchange as fallback
                if available_exchanges:
                    fallback = available_exchanges[0]
                    logger.warning(f"⚠️ [EXCHANGE-VALIDATE] Exchange '{exchange}' not found, using fallback: {fallback}")
                    return fallback
                else:
                    logger.error(f"❌ [EXCHANGE-VALIDATE] No exchange clients available")
                    return 'bybit'  # Final fallback

        except Exception as e:
            logger.error(f"❌ [EXCHANGE-VALIDATE] Error validating exchange '{exchange}': {e}")
            return 'bybit'  # Safe fallback

    async def _get_current_balance(self, currency: str, exchange: str) -> Decimal:
        """Get current balance for a currency on an exchange"""
        try:
            # Check cache first
            if exchange in self.balance_cache:
                cache_data = self.balance_cache[exchange]
                if time.time() - cache_data['last_update'] < self.cache_ttl:
                    balance = cache_data['balances'].get(currency, 0.0)
                    return Decimal(str(balance))
            
            # Fetch fresh balance
            client = self.exchange_clients.get(exchange)
            if client and hasattr(client, 'get_balance'):
                balance = await client.get_balance(currency)
                return Decimal(str(balance)) if balance else Decimal('0')
            
            return Decimal('0')
            
        except Exception as e:
            logger.error(f"❌ [BALANCE] Error getting balance for {currency} on {exchange}: {e}")
            return Decimal('0')

    async def _get_current_price(self, symbol: str, exchange: str) -> Decimal:
        """Get current price for a symbol"""
        try:
            client = self.exchange_clients.get(exchange)
            if client and hasattr(client, 'get_price'):
                # CRITICAL FIX: get_price is not async, don't await it
                price = client.get_price(symbol)
                return Decimal(str(price)) if price else Decimal('0')
            return Decimal('0')
        except Exception as e:
            logger.error(f"❌ [PRICE] Error getting price for {symbol}: {e}")
            return Decimal('0')

    def _calculate_balance_confidence(self, exchange: str) -> float:
        """Calculate confidence in balance data"""
        try:
            if exchange not in self.balance_cache:
                return 0.0
            
            cache_age = time.time() - self.balance_cache[exchange]['last_update']
            if cache_age < 10:  # Very fresh
                return 0.95
            elif cache_age < 30:  # Fresh
                return 0.8
            elif cache_age < 60:  # Acceptable
                return 0.6
            else:  # Stale
                return 0.3
                
        except Exception:
            return 0.5

    async def refresh_all_balances(self):
        """Refresh balance cache for all exchanges"""
        try:
            logger.info("🔄 [BALANCE-REFRESH] Refreshing all balances...")
            
            for exchange_name, client in self.exchange_clients.items():
                try:
                    if hasattr(client, 'get_all_available_balances'):
                        balances = await client.get_all_available_balances()
                        self.balance_cache[exchange_name] = {
                            'balances': balances,
                            'last_update': time.time()
                        }
                        logger.info(f"✅ [BALANCE-REFRESH] Updated {exchange_name}: {len(balances)} currencies")
                    
                except Exception as e:
                    logger.error(f"❌ [BALANCE-REFRESH] Error refreshing {exchange_name}: {e}")
                    
        except Exception as e:
            logger.error(f"❌ [BALANCE-REFRESH] Error refreshing balances: {e}")

    async def get_balance_status_report(self) -> Dict[str, Any]:
        """Generate comprehensive balance status report"""
        try:
            report = {
                'timestamp': time.time(),
                'exchanges': len(self.exchange_clients),
                'total_currencies': 0,
                'balance_by_exchange': {},
                'critical_balances': [],
                'order_statistics': self.order_stats.copy(),
                'recommendations': []
            }

            total_currencies = set()
            critical_balances = []

            for exchange_name, cache_data in self.balance_cache.items():
                balances = cache_data['balances']
                exchange_report = {
                    'currencies': len(balances),
                    'last_update': cache_data['last_update'],
                    'total_value_usd': 0.0,
                    'balances': {}
                }

                for currency, balance in balances.items():
                    balance_float = float(balance) if balance else 0.0
                    total_currencies.add(currency)
                    
                    exchange_report['balances'][currency] = balance_float
                    
                    # Check for critical low balances
                    if balance_float > 0 and balance_float < 1.0:  # Less than $1
                        critical_balances.append({
                            'exchange': exchange_name,
                            'currency': currency,
                            'balance': balance_float
                        })

                report['balance_by_exchange'][exchange_name] = exchange_report

            report['total_currencies'] = len(total_currencies)
            report['critical_balances'] = critical_balances

            # Add recommendations
            if len(critical_balances) > 0:
                report['recommendations'].append(f"Found {len(critical_balances)} currencies with low balances")

            if self.order_stats['total_orders'] > 0:
                failure_rate = self.order_stats['balance_failures'] / max(1, self.order_stats['total_orders'])
                if failure_rate > 0.1:  # 10% failure rate
                    report['recommendations'].append("High balance failure rate - consider increasing minimum balances")

            if not report['recommendations']:
                report['recommendations'].append("Balance management is operating normally")

            return report

        except Exception as e:
            logger.error(f"❌ [BALANCE-REPORT] Error generating report: {e}")
            return {'error': str(e)}
